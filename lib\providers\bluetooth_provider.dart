import 'dart:async';
import 'package:flutter/material.dart';
import '../models/bluetooth_device_model.dart';
import '../services/bluetooth_service.dart';
import '../services/alarm_service.dart';
import '../services/permission_service.dart';

/// Provider for managing Bluetooth state and operations
class BluetoothProvider extends ChangeNotifier {
  final BluetoothService _bluetoothService = BluetoothService();
  final AlarmService _alarmService = AlarmService();
  final PermissionService _permissionService = PermissionService();

  // State variables
  bool _isBluetoothEnabled = false;
  bool _isScanning = false;
  bool _isInitialized = false;
  List<BluetoothDeviceModel> _pairedDevices = [];
  List<BluetoothDeviceModel> _scannedDevices = [];
  String? _errorMessage;
  bool _hasRequiredPermissions = false;

  // Stream subscriptions
  StreamSubscription<List<BluetoothDeviceModel>>? _pairedDevicesSubscription;
  StreamSubscription<List<BluetoothDeviceModel>>? _scannedDevicesSubscription;
  StreamSubscription<BluetoothDeviceModel>? _deviceConnectionSubscription;
  StreamSubscription<bool>? _bluetoothStateSubscription;
  StreamSubscription<bool>? _scanningStateSubscription;

  // Getters
  bool get isBluetoothEnabled => _isBluetoothEnabled;
  bool get isScanning => _isScanning;
  bool get isInitialized => _isInitialized;
  List<BluetoothDeviceModel> get pairedDevices => List.unmodifiable(_pairedDevices);
  List<BluetoothDeviceModel> get scannedDevices => List.unmodifiable(_scannedDevices);
  String? get errorMessage => _errorMessage;
  bool get hasRequiredPermissions => _hasRequiredPermissions;

  /// Initialize the provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _setError(null);

      // Check permissions first
      _hasRequiredPermissions = await _permissionService.hasAllRequiredPermissions();
      
      // Initialize services
      await _alarmService.initialize();
      await _bluetoothService.initialize();

      // Set up stream subscriptions
      _setupStreamSubscriptions();

      _isInitialized = true;
      notifyListeners();

    } catch (e) {
      _setError('Failed to initialize Bluetooth: $e');
      debugPrint('Error initializing BluetoothProvider: $e');
    }
  }

  /// Set up stream subscriptions
  void _setupStreamSubscriptions() {
    // Listen to paired devices changes
    _pairedDevicesSubscription = _bluetoothService.pairedDevicesStream.listen((devices) {
      _pairedDevices = devices;
      notifyListeners();
    });

    // Listen to scanned devices changes
    _scannedDevicesSubscription = _bluetoothService.scannedDevicesStream.listen((devices) {
      _scannedDevices = devices;
      notifyListeners();
    });

    // Listen to device connection changes
    _deviceConnectionSubscription = _bluetoothService.deviceConnectionStream.listen((device) {
      // Update device in paired devices list
      final index = _pairedDevices.indexWhere((d) => d.id == device.id);
      if (index >= 0) {
        _pairedDevices[index] = device;
        notifyListeners();
      }

      // Trigger alarm if needed
      _alarmService.onDeviceConnectionChanged(device);
    });

    // Listen to Bluetooth state changes
    _bluetoothStateSubscription = _bluetoothService.bluetoothStateStream.listen((isEnabled) {
      _isBluetoothEnabled = isEnabled;
      if (!isEnabled) {
        _scannedDevices.clear();
        _isScanning = false;
      }
      notifyListeners();
    });

    // Listen to scanning state changes
    _scanningStateSubscription = _bluetoothService.scanningStateStream.listen((isScanning) {
      _isScanning = isScanning;
      notifyListeners();
    });
  }

  /// Request required permissions
  Future<bool> requestPermissions() async {
    try {
      _setError(null);
      final result = await _permissionService.requestAllPermissions();
      _hasRequiredPermissions = result.success;
      
      if (!result.success) {
        _setError(result.message);
      }
      
      notifyListeners();
      return result.success;
    } catch (e) {
      _setError('Failed to request permissions: $e');
      return false;
    }
  }

  /// Turn on Bluetooth
  Future<bool> turnOnBluetooth() async {
    try {
      _setError(null);
      final success = await _bluetoothService.turnOnBluetooth();
      if (!success) {
        _setError('Failed to turn on Bluetooth');
      }
      return success;
    } catch (e) {
      _setError('Error turning on Bluetooth: $e');
      return false;
    }
  }

  /// Start scanning for devices
  Future<bool> startScan({Duration timeout = const Duration(seconds: 15)}) async {
    try {
      _setError(null);

      // Check permissions first
      if (!_hasRequiredPermissions) {
        final permissionGranted = await requestPermissions();
        if (!permissionGranted) {
          return false;
        }
      }

      // Check if Bluetooth is enabled
      if (!_isBluetoothEnabled) {
        _setError('Bluetooth is not enabled');
        return false;
      }

      final success = await _bluetoothService.startScan(timeout: timeout);
      if (!success) {
        _setError('Failed to start scanning');
      }
      
      return success;
    } catch (e) {
      _setError('Error starting scan: $e');
      return false;
    }
  }

  /// Stop scanning for devices
  Future<void> stopScan() async {
    try {
      await _bluetoothService.stopScan();
    } catch (e) {
      _setError('Error stopping scan: $e');
    }
  }

  /// Connect to a device
  Future<bool> connectToDevice(String deviceId) async {
    try {
      _setError(null);
      final success = await _bluetoothService.connectToDevice(deviceId);
      if (!success) {
        _setError('Failed to connect to device');
      }
      return success;
    } catch (e) {
      _setError('Error connecting to device: $e');
      return false;
    }
  }

  /// Disconnect from a device
  Future<bool> disconnectFromDevice(String deviceId) async {
    try {
      _setError(null);
      final success = await _bluetoothService.disconnectFromDevice(deviceId);
      if (!success) {
        _setError('Failed to disconnect from device');
      }
      return success;
    } catch (e) {
      _setError('Error disconnecting from device: $e');
      return false;
    }
  }

  /// Get device by ID
  BluetoothDeviceModel? getDeviceById(String deviceId) {
    // Check paired devices first
    try {
      return _pairedDevices.firstWhere((device) => device.id == deviceId);
    } catch (e) {
      // Check scanned devices
      try {
        return _scannedDevices.firstWhere((device) => device.id == deviceId);
      } catch (e) {
        return null;
      }
    }
  }

  /// Update device alarm settings
  Future<void> updateDeviceAlarmSettings(String deviceId, bool isEnabled) async {
    try {
      final device = getDeviceById(deviceId);
      if (device != null) {
        final updatedDevice = device.copyWith(isAlarmEnabled: isEnabled);
        
        // Update in paired devices list
        final index = _pairedDevices.indexWhere((d) => d.id == deviceId);
        if (index >= 0) {
          _pairedDevices[index] = updatedDevice;
          notifyListeners();
        }
      }
    } catch (e) {
      _setError('Error updating device alarm settings: $e');
    }
  }

  /// Refresh paired devices
  Future<void> refreshPairedDevices() async {
    try {
      _setError(null);
      // The service will automatically emit updated devices through the stream
      await _bluetoothService.initialize();
    } catch (e) {
      _setError('Error refreshing paired devices: $e');
    }
  }

  /// Clear error message
  void clearError() {
    _setError(null);
  }

  /// Set error message
  void _setError(String? error) {
    _errorMessage = error;
    if (error != null) {
      debugPrint('BluetoothProvider error: $error');
    }
    notifyListeners();
  }

  /// Get connection status for a device
  bool isDeviceConnected(String deviceId) {
    final device = getDeviceById(deviceId);
    return device?.isConnected ?? false;
  }

  /// Get alarm status for a device
  bool isDeviceAlarmEnabled(String deviceId) {
    final device = getDeviceById(deviceId);
    return device?.isAlarmEnabled ?? false;
  }

  @override
  void dispose() {
    // Cancel all subscriptions
    _pairedDevicesSubscription?.cancel();
    _scannedDevicesSubscription?.cancel();
    _deviceConnectionSubscription?.cancel();
    _bluetoothStateSubscription?.cancel();
    _scanningStateSubscription?.cancel();

    // Dispose services
    _bluetoothService.dispose();
    _alarmService.dispose();

    super.dispose();
  }
}
