import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bluetooth_provider.dart';
import '../widgets/device_tile.dart';

/// Screen for scanning and discovering Bluetooth devices
class ScanningScreen extends StatefulWidget {
  const ScanningScreen({super.key});

  @override
  State<ScanningScreen> createState() => _ScanningScreenState();
}

class _ScanningScreenState extends State<ScanningScreen> {
  final Set<String> _pairingDevices = {};

  @override
  void initState() {
    super.initState();
    _startScanning();
  }

  @override
  void dispose() {
    _stopScanning();
    super.dispose();
  }

  Future<void> _startScanning() async {
    final bluetoothProvider = context.read<BluetoothProvider>();
    await bluetoothProvider.startScan();
  }

  Future<void> _stopScanning() async {
    final bluetoothProvider = context.read<BluetoothProvider>();
    await bluetoothProvider.stopScan();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Scan for Devices'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer<BluetoothProvider>(
            builder: (context, bluetoothProvider, child) {
              return IconButton(
                icon: Icon(
                  bluetoothProvider.isScanning ? Icons.stop : Icons.refresh,
                ),
                onPressed: bluetoothProvider.isScanning
                    ? _stopScanning
                    : _startScanning,
                tooltip: bluetoothProvider.isScanning ? 'Stop Scan' : 'Refresh',
              );
            },
          ),
        ],
      ),
      body: Consumer<BluetoothProvider>(
        builder: (context, bluetoothProvider, child) {
          if (bluetoothProvider.errorMessage != null) {
            return _buildErrorView(bluetoothProvider);
          }

          return Column(
            children: [
              _buildScanningHeader(bluetoothProvider),
              Expanded(
                child: _buildDeviceList(bluetoothProvider),
              ),
            ],
          );
        },
      ),
      floatingActionButton: Consumer<BluetoothProvider>(
        builder: (context, bluetoothProvider, child) {
          return FloatingActionButton.extended(
            onPressed:
                bluetoothProvider.isScanning ? _stopScanning : _startScanning,
            icon: bluetoothProvider.isScanning
                ? const Icon(Icons.stop)
                : const Icon(Icons.bluetooth_searching),
            label:
                Text(bluetoothProvider.isScanning ? 'Stop Scan' : 'Start Scan'),
            backgroundColor: bluetoothProvider.isScanning
                ? Colors.red
                : Theme.of(context).primaryColor,
          );
        },
      ),
    );
  }

  Widget _buildErrorView(BluetoothProvider bluetoothProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Scanning Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              bluetoothProvider.errorMessage!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                bluetoothProvider.clearError();
                _startScanning();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScanningHeader(BluetoothProvider bluetoothProvider) {
    final scannedDevices = bluetoothProvider.scannedDevices;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              if (bluetoothProvider.isScanning)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              else
                Icon(
                  Icons.bluetooth_searching,
                  color: Theme.of(context).primaryColor,
                ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bluetoothProvider.isScanning
                          ? 'Scanning for devices...'
                          : 'Scan completed',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '${scannedDevices.length} device${scannedDevices.length != 1 ? 's' : ''} found',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (bluetoothProvider.isScanning) ...[
            const SizedBox(height: 12),
            LinearProgressIndicator(
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDeviceList(BluetoothProvider bluetoothProvider) {
    final scannedDevices = bluetoothProvider.scannedDevices;

    if (scannedDevices.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                bluetoothProvider.isScanning
                    ? Icons.bluetooth_searching
                    : Icons.bluetooth_disabled,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                bluetoothProvider.isScanning
                    ? 'Searching for devices...'
                    : 'No devices found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                bluetoothProvider.isScanning
                    ? 'Make sure nearby devices are discoverable'
                    : 'Try scanning again or check if devices are nearby and discoverable',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: scannedDevices.length,
      itemBuilder: (context, index) {
        final device = scannedDevices[index];
        final isPairing = _pairingDevices.contains(device.id);

        return ScanDeviceTile(
          device: device,
          isPairing: isPairing,
          onTap: () => _pairWithDevice(device.id),
        );
      },
    );
  }

  Future<void> _pairWithDevice(String deviceId) async {
    setState(() {
      _pairingDevices.add(deviceId);
    });

    try {
      final bluetoothProvider = context.read<BluetoothProvider>();
      final success = await bluetoothProvider.connectToDevice(deviceId);

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Device paired successfully!'),
              backgroundColor: Colors.green,
            ),
          );

          // Refresh paired devices and go back
          await bluetoothProvider.refreshPairedDevices();
          Navigator.of(context).pop();
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to pair with device'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error pairing device: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _pairingDevices.remove(deviceId);
        });
      }
    }
  }
}
