import 'bluetooth_device_model.dart';

/// Settings for device-specific alarms
class AlarmSettings {
  final String deviceId;
  final bool isEnabled;
  final AlarmTriggerType triggerType;
  final String soundPath;
  final double volume;
  final bool vibrate;
  final int duration; // in seconds
  final bool repeatAlarm;
  final int repeatInterval; // in seconds
  final int maxRepeats;
  final bool showNotification;
  final String notificationTitle;
  final String notificationMessage;

  const AlarmSettings({
    required this.deviceId,
    this.isEnabled = false,
    this.triggerType = AlarmTriggerType.onDisconnect,
    this.soundPath = 'assets/sounds/default_alarm.mp3',
    this.volume = 0.8,
    this.vibrate = true,
    this.duration = 10,
    this.repeatAlarm = false,
    this.repeatInterval = 30,
    this.maxRepeats = 3,
    this.showNotification = true,
    this.notificationTitle = 'Bluetooth Device Alert',
    this.notificationMessage = 'Device connection status changed',
  });

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'deviceId': deviceId,
      'isEnabled': isEnabled,
      'triggerType': triggerType.index,
      'soundPath': soundPath,
      'volume': volume,
      'vibrate': vibrate,
      'duration': duration,
      'repeatAlarm': repeatAlarm,
      'repeatInterval': repeatInterval,
      'maxRepeats': maxRepeats,
      'showNotification': showNotification,
      'notificationTitle': notificationTitle,
      'notificationMessage': notificationMessage,
    };
  }

  /// Create from JSON
  factory AlarmSettings.fromJson(Map<String, dynamic> json) {
    return AlarmSettings(
      deviceId: json['deviceId'],
      isEnabled: json['isEnabled'] ?? false,
      triggerType: AlarmTriggerType.values[json['triggerType'] ?? 0],
      soundPath: json['soundPath'] ?? 'assets/sounds/default_alarm.mp3',
      volume: (json['volume'] ?? 0.8).toDouble(),
      vibrate: json['vibrate'] ?? true,
      duration: json['duration'] ?? 10,
      repeatAlarm: json['repeatAlarm'] ?? false,
      repeatInterval: json['repeatInterval'] ?? 30,
      maxRepeats: json['maxRepeats'] ?? 3,
      showNotification: json['showNotification'] ?? true,
      notificationTitle: json['notificationTitle'] ?? 'Bluetooth Device Alert',
      notificationMessage:
          json['notificationMessage'] ?? 'Device connection status changed',
    );
  }

  /// Copy with new values
  AlarmSettings copyWith({
    String? deviceId,
    bool? isEnabled,
    AlarmTriggerType? triggerType,
    String? soundPath,
    double? volume,
    bool? vibrate,
    int? duration,
    bool? repeatAlarm,
    int? repeatInterval,
    int? maxRepeats,
    bool? showNotification,
    String? notificationTitle,
    String? notificationMessage,
  }) {
    return AlarmSettings(
      deviceId: deviceId ?? this.deviceId,
      isEnabled: isEnabled ?? this.isEnabled,
      triggerType: triggerType ?? this.triggerType,
      soundPath: soundPath ?? this.soundPath,
      volume: volume ?? this.volume,
      vibrate: vibrate ?? this.vibrate,
      duration: duration ?? this.duration,
      repeatAlarm: repeatAlarm ?? this.repeatAlarm,
      repeatInterval: repeatInterval ?? this.repeatInterval,
      maxRepeats: maxRepeats ?? this.maxRepeats,
      showNotification: showNotification ?? this.showNotification,
      notificationTitle: notificationTitle ?? this.notificationTitle,
      notificationMessage: notificationMessage ?? this.notificationMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AlarmSettings && other.deviceId == deviceId;
  }

  @override
  int get hashCode => deviceId.hashCode;

  @override
  String toString() {
    return 'AlarmSettings(deviceId: $deviceId, isEnabled: $isEnabled, triggerType: $triggerType)';
  }
}

/// Predefined alarm sounds
class AlarmSounds {
  static const String defaultAlarm = 'assets/sounds/default_alarm.mp3';
  static const String beep = 'assets/sounds/beep.mp3';
  static const String chime = 'assets/sounds/chime.mp3';
  static const String alert = 'assets/sounds/alert.mp3';

  static const List<AlarmSound> availableSounds = [
    AlarmSound(name: 'Default Alarm', path: defaultAlarm),
    AlarmSound(name: 'Beep', path: beep),
    AlarmSound(name: 'Chime', path: chime),
    AlarmSound(name: 'Alert', path: alert),
  ];
}

/// Represents an alarm sound option
class AlarmSound {
  final String name;
  final String path;

  const AlarmSound({
    required this.name,
    required this.path,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AlarmSound && other.path == path;
  }

  @override
  int get hashCode => path.hashCode;

  @override
  String toString() => 'AlarmSound(name: $name, path: $path)';
}
