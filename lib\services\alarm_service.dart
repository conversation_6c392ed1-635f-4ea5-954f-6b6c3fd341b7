import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/alarm_settings.dart';
import '../models/bluetooth_device_model.dart';

/// Service to handle alarm functionality
class AlarmService {
  static final AlarmService _instance = AlarmService._internal();
  factory AlarmService() => _instance;
  AlarmService._internal();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final FlutterLocalNotificationsPlugin _notificationsPlugin = FlutterLocalNotificationsPlugin();
  
  // Stream controllers
  final StreamController<AlarmEvent> _alarmEventController = 
      StreamController<AlarmEvent>.broadcast();

  // Internal state
  final Map<String, AlarmSettings> _deviceAlarmSettings = {};
  final Map<String, Timer> _activeAlarms = {};
  final Map<String, int> _alarmRepeatCounts = {};
  bool _isInitialized = false;

  // Getters
  Stream<AlarmEvent> get alarmEventStream => _alarmEventController.stream;
  Map<String, AlarmSettings> get deviceAlarmSettings => Map.unmodifiable(_deviceAlarmSettings);

  /// Initialize the alarm service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize notifications
      await _initializeNotifications();
      
      // Load saved alarm settings
      await _loadAlarmSettings();
      
      _isInitialized = true;
      debugPrint('Alarm service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing alarm service: $e');
      rethrow;
    }
  }

  /// Initialize local notifications
  Future<void> _initializeNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notificationsPlugin.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    debugPrint('Notification tapped: ${response.payload}');
    // Handle notification tap - could navigate to specific device or stop alarm
    if (response.payload != null) {
      final deviceId = response.payload!;
      stopAlarm(deviceId);
    }
  }

  /// Load alarm settings from storage
  Future<void> _loadAlarmSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('alarm_settings');
      
      if (settingsJson != null) {
        final Map<String, dynamic> settingsMap = json.decode(settingsJson);
        _deviceAlarmSettings.clear();
        
        for (final entry in settingsMap.entries) {
          _deviceAlarmSettings[entry.key] = AlarmSettings.fromJson(entry.value);
        }
      }
    } catch (e) {
      debugPrint('Error loading alarm settings: $e');
    }
  }

  /// Save alarm settings to storage
  Future<void> _saveAlarmSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsMap = <String, dynamic>{};
      
      for (final entry in _deviceAlarmSettings.entries) {
        settingsMap[entry.key] = entry.value.toJson();
      }
      
      await prefs.setString('alarm_settings', json.encode(settingsMap));
    } catch (e) {
      debugPrint('Error saving alarm settings: $e');
    }
  }

  /// Get alarm settings for a device
  AlarmSettings getAlarmSettings(String deviceId) {
    return _deviceAlarmSettings[deviceId] ?? AlarmSettings(deviceId: deviceId);
  }

  /// Update alarm settings for a device
  Future<void> updateAlarmSettings(AlarmSettings settings) async {
    _deviceAlarmSettings[settings.deviceId] = settings;
    await _saveAlarmSettings();
  }

  /// Handle device connection change
  void onDeviceConnectionChanged(BluetoothDeviceModel device) {
    final settings = _deviceAlarmSettings[device.id];
    if (settings == null || !settings.isEnabled) return;

    final shouldTrigger = _shouldTriggerAlarm(device, settings);
    if (shouldTrigger) {
      triggerAlarm(device, settings);
    }
  }

  /// Check if alarm should be triggered
  bool _shouldTriggerAlarm(BluetoothDeviceModel device, AlarmSettings settings) {
    switch (settings.triggerType) {
      case AlarmTriggerType.onConnect:
        return device.isConnected;
      case AlarmTriggerType.onDisconnect:
        return !device.isConnected;
      case AlarmTriggerType.both:
        return true;
    }
  }

  /// Trigger an alarm for a device
  Future<void> triggerAlarm(BluetoothDeviceModel device, AlarmSettings settings) async {
    try {
      // Stop any existing alarm for this device
      stopAlarm(device.id);

      // Reset repeat count
      _alarmRepeatCounts[device.id] = 0;

      // Start the alarm
      await _startAlarm(device, settings);

      // Emit alarm event
      _alarmEventController.add(AlarmEvent(
        deviceId: device.id,
        deviceName: device.name,
        type: AlarmEventType.triggered,
        triggerType: settings.triggerType,
        timestamp: DateTime.now(),
      ));

    } catch (e) {
      debugPrint('Error triggering alarm for device ${device.id}: $e');
    }
  }

  /// Start playing alarm
  Future<void> _startAlarm(BluetoothDeviceModel device, AlarmSettings settings) async {
    try {
      // Play sound
      await _playAlarmSound(settings);

      // Vibrate if enabled
      if (settings.vibrate) {
        HapticFeedback.vibrate();
      }

      // Show notification
      if (settings.showNotification) {
        await _showAlarmNotification(device, settings);
      }

      // Set timer to stop alarm after duration
      _activeAlarms[device.id] = Timer(Duration(seconds: settings.duration), () {
        _stopAlarmSound();
        
        // Handle repeat if enabled
        if (settings.repeatAlarm && _alarmRepeatCounts[device.id]! < settings.maxRepeats) {
          _alarmRepeatCounts[device.id] = (_alarmRepeatCounts[device.id] ?? 0) + 1;
          
          Timer(Duration(seconds: settings.repeatInterval), () {
            if (_alarmRepeatCounts[device.id]! < settings.maxRepeats) {
              _startAlarm(device, settings);
            }
          });
        }
      });

    } catch (e) {
      debugPrint('Error starting alarm: $e');
    }
  }

  /// Play alarm sound
  Future<void> _playAlarmSound(AlarmSettings settings) async {
    try {
      await _audioPlayer.setVolume(settings.volume);
      await _audioPlayer.play(AssetSource(settings.soundPath.replaceFirst('assets/', '')));
    } catch (e) {
      debugPrint('Error playing alarm sound: $e');
      // Fallback to system sound
      HapticFeedback.heavyImpact();
    }
  }

  /// Stop alarm sound
  Future<void> _stopAlarmSound() async {
    try {
      await _audioPlayer.stop();
    } catch (e) {
      debugPrint('Error stopping alarm sound: $e');
    }
  }

  /// Show alarm notification
  Future<void> _showAlarmNotification(BluetoothDeviceModel device, AlarmSettings settings) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'bluetooth_alarm_channel',
        'Bluetooth Device Alarms',
        channelDescription: 'Notifications for Bluetooth device connection changes',
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      final title = settings.notificationTitle.replaceAll('{deviceName}', device.name);
      final message = settings.notificationMessage.replaceAll('{deviceName}', device.name);

      await _notificationsPlugin.show(
        device.id.hashCode,
        title,
        message,
        notificationDetails,
        payload: device.id,
      );
    } catch (e) {
      debugPrint('Error showing notification: $e');
    }
  }

  /// Stop alarm for a device
  void stopAlarm(String deviceId) {
    _activeAlarms[deviceId]?.cancel();
    _activeAlarms.remove(deviceId);
    _alarmRepeatCounts.remove(deviceId);
    _stopAlarmSound();
    
    // Cancel notification
    _notificationsPlugin.cancel(deviceId.hashCode);

    _alarmEventController.add(AlarmEvent(
      deviceId: deviceId,
      deviceName: '',
      type: AlarmEventType.stopped,
      triggerType: AlarmTriggerType.onDisconnect,
      timestamp: DateTime.now(),
    ));
  }

  /// Stop all active alarms
  void stopAllAlarms() {
    for (final deviceId in _activeAlarms.keys.toList()) {
      stopAlarm(deviceId);
    }
  }

  /// Test alarm for a device
  Future<void> testAlarm(String deviceId) async {
    final settings = getAlarmSettings(deviceId);
    if (!settings.isEnabled) return;

    try {
      await _playAlarmSound(settings);
      
      if (settings.vibrate) {
        HapticFeedback.vibrate();
      }

      // Stop after 3 seconds for testing
      Timer(const Duration(seconds: 3), () {
        _stopAlarmSound();
      });

      _alarmEventController.add(AlarmEvent(
        deviceId: deviceId,
        deviceName: '',
        type: AlarmEventType.tested,
        triggerType: settings.triggerType,
        timestamp: DateTime.now(),
      ));

    } catch (e) {
      debugPrint('Error testing alarm: $e');
    }
  }

  /// Check if device has active alarm
  bool hasActiveAlarm(String deviceId) {
    return _activeAlarms.containsKey(deviceId);
  }

  /// Dispose of the service
  void dispose() {
    stopAllAlarms();
    _audioPlayer.dispose();
    _alarmEventController.close();
  }
}

/// Represents an alarm event
class AlarmEvent {
  final String deviceId;
  final String deviceName;
  final AlarmEventType type;
  final AlarmTriggerType triggerType;
  final DateTime timestamp;

  AlarmEvent({
    required this.deviceId,
    required this.deviceName,
    required this.type,
    required this.triggerType,
    required this.timestamp,
  });
}

/// Types of alarm events
enum AlarmEventType {
  triggered,
  stopped,
  tested,
}
