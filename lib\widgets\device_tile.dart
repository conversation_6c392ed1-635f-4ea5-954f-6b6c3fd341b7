import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/bluetooth_device_model.dart';
import '../providers/alarm_provider.dart';
import '../providers/bluetooth_provider.dart';

/// Widget to display a Bluetooth device in a list
class DeviceTile extends StatelessWidget {
  final BluetoothDeviceModel device;
  final VoidCallback? onTap;
  final bool showAlarmToggle;
  final bool showConnectionButton;

  const DeviceTile({
    super.key,
    required this.device,
    this.onTap,
    this.showAlarmToggle = true,
    this.showConnectionButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Consumer2<BluetoothProvider, AlarmProvider>(
        builder: (context, bluetoothProvider, alarmProvider, child) {
          final isConnected = device.isConnected;
          final hasActiveAlarm = alarmProvider.hasActiveAlarm(device.id);
          final isAlarmEnabled = alarmProvider.isAlarmEnabled(device.id);

          return ListTile(
            leading: _buildDeviceIcon(isConnected, hasActiveAlarm),
            title: Text(
              device.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.address,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    _buildConnectionStatus(isConnected),
                    const SizedBox(width: 8),
                    if (device.rssi != null) _buildSignalStrength(device.rssi!),
                    const Spacer(),
                    if (isAlarmEnabled) _buildAlarmIndicator(hasActiveAlarm),
                  ],
                ),
              ],
            ),
            trailing: _buildTrailingActions(
              context,
              bluetoothProvider,
              alarmProvider,
              isConnected,
              isAlarmEnabled,
            ),
            onTap: onTap,
          );
        },
      ),
    );
  }

  Widget _buildDeviceIcon(bool isConnected, bool hasActiveAlarm) {
    IconData iconData;
    Color iconColor;

    if (hasActiveAlarm) {
      iconData = Icons.alarm;
      iconColor = Colors.red;
    } else if (isConnected) {
      iconData = _getDeviceTypeIcon();
      iconColor = Colors.green;
    } else {
      iconData = _getDeviceTypeIcon();
      iconColor = Colors.grey;
    }

    return Stack(
      children: [
        Icon(iconData, color: iconColor, size: 32),
        if (hasActiveAlarm)
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              width: 12,
              height: 12,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.priority_high,
                color: Colors.white,
                size: 8,
              ),
            ),
          ),
      ],
    );
  }

  IconData _getDeviceTypeIcon() {
    switch (device.type) {
      case DeviceType.classic:
        return Icons.bluetooth;
      case DeviceType.le:
        return Icons.bluetooth_connected;
      case DeviceType.dual:
        return Icons.bluetooth_searching;
      default:
        return Icons.device_unknown;
    }
  }

  Widget _buildConnectionStatus(bool isConnected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: isConnected ? Colors.green : Colors.grey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        isConnected ? 'Connected' : 'Disconnected',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildSignalStrength(int rssi) {
    Color signalColor;

    if (rssi >= -50) {
      signalColor = Colors.green;
    } else if (rssi >= -60) {
      signalColor = Colors.lightGreen;
    } else if (rssi >= -70) {
      signalColor = Colors.orange;
    } else {
      signalColor = Colors.red;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.signal_cellular_alt, color: signalColor, size: 16),
        const SizedBox(width: 2),
        Text(
          '${rssi}dBm',
          style: TextStyle(
            color: signalColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildAlarmIndicator(bool hasActiveAlarm) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: hasActiveAlarm ? Colors.red : Colors.blue,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            hasActiveAlarm ? Icons.alarm : Icons.alarm_on,
            color: Colors.white,
            size: 12,
          ),
          const SizedBox(width: 2),
          Text(
            hasActiveAlarm ? 'ACTIVE' : 'ENABLED',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 8,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrailingActions(
    BuildContext context,
    BluetoothProvider bluetoothProvider,
    AlarmProvider alarmProvider,
    bool isConnected,
    bool isAlarmEnabled,
  ) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (showAlarmToggle)
          IconButton(
            icon: Icon(
              isAlarmEnabled ? Icons.alarm_on : Icons.alarm_off,
              color: isAlarmEnabled ? Colors.blue : Colors.grey,
            ),
            onPressed: () => _toggleAlarm(alarmProvider),
            tooltip: isAlarmEnabled ? 'Disable Alarm' : 'Enable Alarm',
          ),
        if (showConnectionButton)
          IconButton(
            icon: Icon(
              isConnected ? Icons.link_off : Icons.link,
              color: isConnected ? Colors.red : Colors.green,
            ),
            onPressed: () => _toggleConnection(bluetoothProvider, isConnected),
            tooltip: isConnected ? 'Disconnect' : 'Connect',
          ),
      ],
    );
  }

  void _toggleAlarm(AlarmProvider alarmProvider) {
    final isCurrentlyEnabled = alarmProvider.isAlarmEnabled(device.id);
    alarmProvider.toggleDeviceAlarm(device.id, !isCurrentlyEnabled);
  }

  void _toggleConnection(
      BluetoothProvider bluetoothProvider, bool isConnected) {
    if (isConnected) {
      bluetoothProvider.disconnectFromDevice(device.id);
    } else {
      bluetoothProvider.connectToDevice(device.id);
    }
  }
}

/// Simplified device tile for scanning results
class ScanDeviceTile extends StatelessWidget {
  final BluetoothDeviceModel device;
  final VoidCallback? onTap;
  final bool isPairing;

  const ScanDeviceTile({
    super.key,
    required this.device,
    this.onTap,
    this.isPairing = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: ListTile(
        leading: Icon(
          _getDeviceTypeIcon(),
          color: Colors.blue,
          size: 32,
        ),
        title: Text(
          device.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              device.address,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                if (device.rssi != null) _buildSignalStrength(device.rssi!),
                const SizedBox(width: 8),
                _buildDeviceType(),
              ],
            ),
          ],
        ),
        trailing: isPairing
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.add_circle_outline),
        onTap: isPairing ? null : onTap,
      ),
    );
  }

  IconData _getDeviceTypeIcon() {
    switch (device.type) {
      case DeviceType.classic:
        return Icons.bluetooth;
      case DeviceType.le:
        return Icons.bluetooth_connected;
      case DeviceType.dual:
        return Icons.bluetooth_searching;
      default:
        return Icons.device_unknown;
    }
  }

  Widget _buildSignalStrength(int rssi) {
    Color signalColor;

    if (rssi >= -50) {
      signalColor = Colors.green;
    } else if (rssi >= -60) {
      signalColor = Colors.lightGreen;
    } else if (rssi >= -70) {
      signalColor = Colors.orange;
    } else {
      signalColor = Colors.red;
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.signal_cellular_alt, color: signalColor, size: 16),
        const SizedBox(width: 2),
        Text(
          '${rssi}dBm',
          style: TextStyle(
            color: signalColor,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceType() {
    String typeText;
    switch (device.type) {
      case DeviceType.classic:
        typeText = 'Classic';
        break;
      case DeviceType.le:
        typeText = 'BLE';
        break;
      case DeviceType.dual:
        typeText = 'Dual';
        break;
      default:
        typeText = 'Unknown';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        typeText,
        style: const TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
