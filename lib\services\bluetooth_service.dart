import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import '../models/bluetooth_device_model.dart';
import 'permission_service.dart';

/// Service to handle all Bluetooth operations
class BluetoothService {
  static final BluetoothService _instance = BluetoothService._internal();
  factory BluetoothService() => _instance;
  BluetoothService._internal();

  final PermissionService _permissionService = PermissionService();

  // Stream controllers
  final StreamController<List<BluetoothDeviceModel>> _pairedDevicesController =
      StreamController<List<BluetoothDeviceModel>>.broadcast();
  final StreamController<List<BluetoothDeviceModel>> _scannedDevicesController =
      StreamController<List<BluetoothDeviceModel>>.broadcast();
  final StreamController<BluetoothDeviceModel> _deviceConnectionController =
      StreamController<BluetoothDeviceModel>.broadcast();
  final StreamController<bool> _bluetoothStateController =
      StreamController<bool>.broadcast();
  final StreamController<bool> _scanningStateController =
      StreamController<bool>.broadcast();

  // Internal state
  bool _isScanning = false;
  List<BluetoothDeviceModel> _pairedDevices = [];
  final List<BluetoothDeviceModel> _scannedDevices = [];
  StreamSubscription<BluetoothAdapterState>? _adapterStateSubscription;
  StreamSubscription<List<ScanResult>>? _scanSubscription;
  final Map<String, StreamSubscription<BluetoothConnectionState>>
      _connectionSubscriptions = {};

  // Getters for streams
  Stream<List<BluetoothDeviceModel>> get pairedDevicesStream =>
      _pairedDevicesController.stream;
  Stream<List<BluetoothDeviceModel>> get scannedDevicesStream =>
      _scannedDevicesController.stream;
  Stream<BluetoothDeviceModel> get deviceConnectionStream =>
      _deviceConnectionController.stream;
  Stream<bool> get bluetoothStateStream => _bluetoothStateController.stream;
  Stream<bool> get scanningStateStream => _scanningStateController.stream;

  // Getters for current state
  bool get isScanning => _isScanning;
  List<BluetoothDeviceModel> get pairedDevices =>
      List.unmodifiable(_pairedDevices);
  List<BluetoothDeviceModel> get scannedDevices =>
      List.unmodifiable(_scannedDevices);

  /// Initialize the Bluetooth service
  Future<void> initialize() async {
    try {
      // Check if Bluetooth is supported
      if (await FlutterBluePlus.isSupported == false) {
        throw Exception('Bluetooth not supported by this device');
      }

      // Listen to adapter state changes
      _adapterStateSubscription = FlutterBluePlus.adapterState.listen((state) {
        _bluetoothStateController.add(state == BluetoothAdapterState.on);
        if (state == BluetoothAdapterState.on) {
          _loadPairedDevices();
        }
      });

      // Check current adapter state
      final currentState = await FlutterBluePlus.adapterState.first;
      _bluetoothStateController.add(currentState == BluetoothAdapterState.on);

      if (currentState == BluetoothAdapterState.on) {
        await _loadPairedDevices();
      }
    } catch (e) {
      debugPrint('Error initializing Bluetooth service: $e');
      rethrow;
    }
  }

  /// Check if Bluetooth is enabled
  Future<bool> isBluetoothEnabled() async {
    try {
      final state = await FlutterBluePlus.adapterState.first;
      return state == BluetoothAdapterState.on;
    } catch (e) {
      debugPrint('Error checking Bluetooth state: $e');
      return false;
    }
  }

  /// Turn on Bluetooth (Android only)
  Future<bool> turnOnBluetooth() async {
    if (!Platform.isAndroid) return false;

    try {
      await FlutterBluePlus.turnOn();
      return true;
    } catch (e) {
      debugPrint('Error turning on Bluetooth: $e');
      return false;
    }
  }

  /// Load paired devices
  Future<void> _loadPairedDevices() async {
    try {
      // Check permissions first
      if (!(await _permissionService.hasAllRequiredPermissions())) {
        debugPrint('Missing required permissions for loading paired devices');
        return;
      }

      final bondedDevices = await FlutterBluePlus.bondedDevices;
      _pairedDevices = bondedDevices
          .map((device) => BluetoothDeviceModel.fromBluetoothDevice(device))
          .toList();

      // Set up connection monitoring for paired devices
      for (final device in _pairedDevices) {
        _monitorDeviceConnection(device.id);
      }

      _pairedDevicesController.add(_pairedDevices);
    } catch (e) {
      debugPrint('Error loading paired devices: $e');
    }
  }

  /// Start scanning for devices
  Future<bool> startScan(
      {Duration timeout = const Duration(seconds: 15)}) async {
    try {
      // Check permissions
      if (!(await _permissionService.hasAllRequiredPermissions())) {
        final result = await _permissionService.requestAllPermissions();
        if (!result.success) {
          throw Exception(
              'Required permissions not granted: ${result.message}');
        }
      }

      // Check if Bluetooth is enabled
      if (!(await isBluetoothEnabled())) {
        throw Exception('Bluetooth is not enabled');
      }

      if (_isScanning) {
        await stopScan();
      }

      _scannedDevices.clear();
      _scannedDevicesController.add(_scannedDevices);

      _isScanning = true;
      _scanningStateController.add(true);

      // Start scanning
      _scanSubscription = FlutterBluePlus.scanResults.listen((results) {
        _updateScannedDevices(results);
      });

      await FlutterBluePlus.startScan(timeout: timeout);

      // Auto-stop scanning after timeout
      Timer(timeout, () {
        if (_isScanning) {
          stopScan();
        }
      });

      return true;
    } catch (e) {
      debugPrint('Error starting scan: $e');
      _isScanning = false;
      _scanningStateController.add(false);
      return false;
    }
  }

  /// Stop scanning for devices
  Future<void> stopScan() async {
    try {
      await FlutterBluePlus.stopScan();
      _scanSubscription?.cancel();
      _isScanning = false;
      _scanningStateController.add(false);
    } catch (e) {
      debugPrint('Error stopping scan: $e');
    }
  }

  /// Update scanned devices list
  void _updateScannedDevices(List<ScanResult> results) {
    for (final result in results) {
      final deviceModel = BluetoothDeviceModel.fromScanResult(result);

      // Skip if already in paired devices
      if (_pairedDevices.any((d) => d.id == deviceModel.id)) {
        continue;
      }

      // Update existing or add new
      final existingIndex =
          _scannedDevices.indexWhere((d) => d.id == deviceModel.id);
      if (existingIndex >= 0) {
        _scannedDevices[existingIndex] = deviceModel;
      } else {
        _scannedDevices.add(deviceModel);
      }
    }

    _scannedDevicesController.add(_scannedDevices);
  }

  /// Connect to a device
  Future<bool> connectToDevice(String deviceId) async {
    try {
      final device = FlutterBluePlus.connectedDevices
          .firstWhere((d) => d.remoteId.str == deviceId);

      if (device.isConnected) {
        return true;
      }

      await device.connect();
      return true;
    } catch (e) {
      debugPrint('Error connecting to device $deviceId: $e');
      return false;
    }
  }

  /// Disconnect from a device
  Future<bool> disconnectFromDevice(String deviceId) async {
    try {
      final device = FlutterBluePlus.connectedDevices
          .firstWhere((d) => d.remoteId.str == deviceId);

      await device.disconnect();
      return true;
    } catch (e) {
      debugPrint('Error disconnecting from device $deviceId: $e');
      return false;
    }
  }

  /// Monitor device connection status
  void _monitorDeviceConnection(String deviceId) {
    try {
      final device = FlutterBluePlus.connectedDevices
          .firstWhere((d) => d.remoteId.str == deviceId);

      _connectionSubscriptions[deviceId]?.cancel();
      _connectionSubscriptions[deviceId] =
          device.connectionState.listen((state) {
        final isConnected = state == BluetoothConnectionState.connected;

        // Update paired device status
        final deviceIndex = _pairedDevices.indexWhere((d) => d.id == deviceId);
        if (deviceIndex >= 0) {
          _pairedDevices[deviceIndex] = _pairedDevices[deviceIndex].copyWith(
            isConnected: isConnected,
            lastSeen: DateTime.now(),
          );
          _pairedDevicesController.add(_pairedDevices);
          _deviceConnectionController.add(_pairedDevices[deviceIndex]);
        }
      });
    } catch (e) {
      debugPrint('Error monitoring device connection $deviceId: $e');
    }
  }

  /// Dispose of the service
  void dispose() {
    _adapterStateSubscription?.cancel();
    _scanSubscription?.cancel();
    for (final subscription in _connectionSubscriptions.values) {
      subscription.cancel();
    }
    _connectionSubscriptions.clear();

    _pairedDevicesController.close();
    _scannedDevicesController.close();
    _deviceConnectionController.close();
    _bluetoothStateController.close();
    _scanningStateController.close();
  }
}
