import 'dart:io';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';

/// Service to handle all permission requests for the app
class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// Check if all required permissions are granted
  Future<bool> hasAllRequiredPermissions() async {
    if (!Platform.isAndroid) return true;

    final androidInfo = await DeviceInfoPlugin().androidInfo;
    final sdkInt = androidInfo.version.sdkInt;

    List<Permission> requiredPermissions = [];

    // Android 12+ permissions
    if (sdkInt >= 31) {
      requiredPermissions.addAll([
        Permission.bluetoothScan,
        Permission.bluetoothConnect,
        Permission.notification,
      ]);
    } else {
      // Pre-Android 12 permissions
      requiredPermissions.addAll([
        Permission.bluetooth,
        Permission.location,
      ]);
    }

    // Common permissions for all versions
    requiredPermissions.addAll([
      Permission.bluetoothAdvertise,
    ]);

    for (Permission permission in requiredPermissions) {
      if (!(await permission.isGranted)) {
        return false;
      }
    }

    return true;
  }

  /// Request all required permissions
  Future<PermissionResult> requestAllPermissions() async {
    if (!Platform.isAndroid) {
      return PermissionResult(success: true, message: 'iOS permissions handled automatically');
    }

    try {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      List<Permission> permissionsToRequest = [];

      // Android 12+ permissions
      if (sdkInt >= 31) {
        permissionsToRequest.addAll([
          Permission.bluetoothScan,
          Permission.bluetoothConnect,
          Permission.notification,
        ]);
      } else {
        // Pre-Android 12 permissions
        permissionsToRequest.addAll([
          Permission.bluetooth,
          Permission.location,
        ]);
      }

      // Request permissions
      Map<Permission, PermissionStatus> statuses = await permissionsToRequest.request();

      // Check if all permissions were granted
      List<Permission> deniedPermissions = [];
      for (var entry in statuses.entries) {
        if (!entry.value.isGranted) {
          deniedPermissions.add(entry.key);
        }
      }

      if (deniedPermissions.isEmpty) {
        return PermissionResult(success: true, message: 'All permissions granted');
      } else {
        String deniedList = deniedPermissions.map((p) => _getPermissionName(p)).join(', ');
        return PermissionResult(
          success: false,
          message: 'The following permissions were denied: $deniedList',
          deniedPermissions: deniedPermissions,
        );
      }
    } catch (e) {
      return PermissionResult(
        success: false,
        message: 'Error requesting permissions: $e',
      );
    }
  }

  /// Request specific permission
  Future<bool> requestPermission(Permission permission) async {
    if (!Platform.isAndroid) return true;

    try {
      final status = await permission.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('Error requesting permission $permission: $e');
      return false;
    }
  }

  /// Check if a specific permission is granted
  Future<bool> isPermissionGranted(Permission permission) async {
    if (!Platform.isAndroid) return true;

    try {
      return await permission.isGranted;
    } catch (e) {
      debugPrint('Error checking permission $permission: $e');
      return false;
    }
  }

  /// Open app settings for manual permission management
  Future<bool> openAppSettings() async {
    try {
      return await openAppSettings();
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  /// Get user-friendly permission name
  String _getPermissionName(Permission permission) {
    switch (permission) {
      case Permission.bluetooth:
        return 'Bluetooth';
      case Permission.bluetoothScan:
        return 'Bluetooth Scan';
      case Permission.bluetoothConnect:
        return 'Bluetooth Connect';
      case Permission.bluetoothAdvertise:
        return 'Bluetooth Advertise';
      case Permission.location:
        return 'Location';
      case Permission.notification:
        return 'Notifications';
      default:
        return permission.toString();
    }
  }

  /// Get permission explanation for user
  String getPermissionExplanation(Permission permission) {
    switch (permission) {
      case Permission.bluetooth:
        return 'Required to access Bluetooth functionality on your device';
      case Permission.bluetoothScan:
        return 'Required to scan for nearby Bluetooth devices';
      case Permission.bluetoothConnect:
        return 'Required to connect to Bluetooth devices';
      case Permission.bluetoothAdvertise:
        return 'Required for advanced Bluetooth features';
      case Permission.location:
        return 'Required for Bluetooth scanning on Android versions before 12';
      case Permission.notification:
        return 'Required to show alarm notifications when devices connect/disconnect';
      default:
        return 'Required for app functionality';
    }
  }

  /// Show permission rationale dialog
  Future<bool> showPermissionRationale(BuildContext context, List<Permission> permissions) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Permissions Required'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('This app needs the following permissions to work properly:'),
              const SizedBox(height: 16),
              ...permissions.map((permission) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getPermissionName(permission),
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            getPermissionExplanation(permission),
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Grant Permissions'),
            ),
          ],
        );
      },
    ) ?? false;
  }
}

/// Result of permission request
class PermissionResult {
  final bool success;
  final String message;
  final List<Permission>? deniedPermissions;

  PermissionResult({
    required this.success,
    required this.message,
    this.deniedPermissions,
  });
}
