import 'dart:async';
import 'package:flutter/material.dart';
import '../models/alarm_settings.dart';
import '../models/bluetooth_device_model.dart';
import '../services/alarm_service.dart';

/// Provider for managing alarm state and operations
class AlarmProvider extends ChangeNotifier {
  final AlarmService _alarmService = AlarmService();

  // State variables
  Map<String, AlarmSettings> _deviceAlarmSettings = {};
  List<AlarmEvent> _recentAlarmEvents = [];
  String? _errorMessage;
  bool _isInitialized = false;

  // Stream subscription
  StreamSubscription<AlarmEvent>? _alarmEventSubscription;

  // Getters
  Map<String, AlarmSettings> get deviceAlarmSettings =>
      Map.unmodifiable(_deviceAlarmSettings);
  List<AlarmEvent> get recentAlarmEvents =>
      List.unmodifiable(_recentAlarmEvents);
  String? get errorMessage => _errorMessage;
  bool get isInitialized => _isInitialized;

  /// Initialize the provider
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _setError(null);

      // Initialize alarm service
      await _alarmService.initialize();

      // Load existing alarm settings
      _deviceAlarmSettings = _alarmService.deviceAlarmSettings;

      // Set up stream subscription for alarm events
      _alarmEventSubscription = _alarmService.alarmEventStream.listen((event) {
        _addAlarmEvent(event);
      });

      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      _setError('Failed to initialize alarm service: $e');
      debugPrint('Error initializing AlarmProvider: $e');
    }
  }

  /// Get alarm settings for a device
  AlarmSettings getAlarmSettings(String deviceId) {
    return _deviceAlarmSettings[deviceId] ?? AlarmSettings(deviceId: deviceId);
  }

  /// Update alarm settings for a device
  Future<void> updateAlarmSettings(AlarmSettings settings) async {
    try {
      _setError(null);

      await _alarmService.updateAlarmSettings(settings);
      _deviceAlarmSettings[settings.deviceId] = settings;

      notifyListeners();
    } catch (e) {
      _setError('Failed to update alarm settings: $e');
    }
  }

  /// Enable/disable alarm for a device
  Future<void> toggleDeviceAlarm(String deviceId, bool isEnabled) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings = currentSettings.copyWith(isEnabled: isEnabled);
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to toggle device alarm: $e');
    }
  }

  /// Update alarm trigger type for a device
  Future<void> updateAlarmTriggerType(
      String deviceId, AlarmTriggerType triggerType) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings =
          currentSettings.copyWith(triggerType: triggerType);
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to update alarm trigger type: $e');
    }
  }

  /// Update alarm sound for a device
  Future<void> updateAlarmSound(String deviceId, String soundPath) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings = currentSettings.copyWith(soundPath: soundPath);
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to update alarm sound: $e');
    }
  }

  /// Update alarm volume for a device
  Future<void> updateAlarmVolume(String deviceId, double volume) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings = currentSettings.copyWith(volume: volume);
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to update alarm volume: $e');
    }
  }

  /// Update alarm duration for a device
  Future<void> updateAlarmDuration(String deviceId, int duration) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings = currentSettings.copyWith(duration: duration);
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to update alarm duration: $e');
    }
  }

  /// Update vibration setting for a device
  Future<void> updateVibrationSetting(String deviceId, bool vibrate) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings = currentSettings.copyWith(vibrate: vibrate);
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to update vibration setting: $e');
    }
  }

  /// Update repeat alarm settings
  Future<void> updateRepeatSettings(
    String deviceId, {
    bool? repeatAlarm,
    int? repeatInterval,
    int? maxRepeats,
  }) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings = currentSettings.copyWith(
        repeatAlarm: repeatAlarm,
        repeatInterval: repeatInterval,
        maxRepeats: maxRepeats,
      );
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to update repeat settings: $e');
    }
  }

  /// Update notification settings
  Future<void> updateNotificationSettings(
    String deviceId, {
    bool? showNotification,
    String? notificationTitle,
    String? notificationMessage,
  }) async {
    try {
      final currentSettings = getAlarmSettings(deviceId);
      final updatedSettings = currentSettings.copyWith(
        showNotification: showNotification,
        notificationTitle: notificationTitle,
        notificationMessage: notificationMessage,
      );
      await updateAlarmSettings(updatedSettings);
    } catch (e) {
      _setError('Failed to update notification settings: $e');
    }
  }

  /// Test alarm for a device
  Future<void> testAlarm(String deviceId) async {
    try {
      _setError(null);
      await _alarmService.testAlarm(deviceId);
    } catch (e) {
      _setError('Failed to test alarm: $e');
    }
  }

  /// Stop alarm for a device
  void stopAlarm(String deviceId) {
    try {
      _alarmService.stopAlarm(deviceId);
    } catch (e) {
      _setError('Failed to stop alarm: $e');
    }
  }

  /// Stop all active alarms
  void stopAllAlarms() {
    try {
      _alarmService.stopAllAlarms();
    } catch (e) {
      _setError('Failed to stop all alarms: $e');
    }
  }

  /// Check if device has active alarm
  bool hasActiveAlarm(String deviceId) {
    return _alarmService.hasActiveAlarm(deviceId);
  }

  /// Check if device has alarm enabled
  bool isAlarmEnabled(String deviceId) {
    return getAlarmSettings(deviceId).isEnabled;
  }

  /// Get devices with alarms enabled
  List<String> getDevicesWithAlarmsEnabled() {
    return _deviceAlarmSettings.entries
        .where((entry) => entry.value.isEnabled)
        .map((entry) => entry.key)
        .toList();
  }

  /// Get count of active alarms
  int getActiveAlarmCount() {
    return getDevicesWithAlarmsEnabled()
        .where((deviceId) => hasActiveAlarm(deviceId))
        .length;
  }

  /// Add alarm event to recent events
  void _addAlarmEvent(AlarmEvent event) {
    _recentAlarmEvents.insert(0, event);

    // Keep only last 50 events
    if (_recentAlarmEvents.length > 50) {
      _recentAlarmEvents = _recentAlarmEvents.take(50).toList();
    }

    notifyListeners();
  }

  /// Clear recent alarm events
  void clearRecentEvents() {
    _recentAlarmEvents.clear();
    notifyListeners();
  }

  /// Get alarm events for a specific device
  List<AlarmEvent> getDeviceAlarmEvents(String deviceId) {
    return _recentAlarmEvents
        .where((event) => event.deviceId == deviceId)
        .toList();
  }

  /// Get alarm events by type
  List<AlarmEvent> getAlarmEventsByType(AlarmEventType type) {
    return _recentAlarmEvents.where((event) => event.type == type).toList();
  }

  /// Clear error message
  void clearError() {
    _setError(null);
  }

  /// Set error message
  void _setError(String? error) {
    _errorMessage = error;
    if (error != null) {
      debugPrint('AlarmProvider error: $error');
    }
    notifyListeners();
  }

  /// Create default alarm settings for a device
  AlarmSettings createDefaultAlarmSettings(String deviceId, String deviceName) {
    return AlarmSettings(
      deviceId: deviceId,
      notificationTitle: 'Bluetooth Alert - $deviceName',
      notificationMessage: '$deviceName connection status changed',
    );
  }

  @override
  void dispose() {
    _alarmEventSubscription?.cancel();
    _alarmService.dispose();
    super.dispose();
  }
}
