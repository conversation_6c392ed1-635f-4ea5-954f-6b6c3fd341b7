import 'package:flutter_blue_plus/flutter_blue_plus.dart';

/// Types of Bluetooth devices
enum DeviceType {
  classic,
  le,
  dual,
  unknown,
}

/// Represents a Bluetooth device with its connection status and alarm settings
class BluetoothDeviceModel {
  final String id;
  final String name;
  final String address;
  final DeviceType type;
  bool isConnected;
  bool isAlarmEnabled;
  AlarmTriggerType alarmTriggerType;
  String? customAlarmSound;
  DateTime? lastSeen;
  int? rssi;

  BluetoothDeviceModel({
    required this.id,
    required this.name,
    required this.address,
    required this.type,
    this.isConnected = false,
    this.isAlarmEnabled = false,
    this.alarmTriggerType = AlarmTriggerType.onDisconnect,
    this.customAlarmSound,
    this.lastSeen,
    this.rssi,
  });

  /// Create from FlutterBluePlus BluetoothDevice
  factory BluetoothDeviceModel.fromBluetoothDevice(BluetoothDevice device) {
    return BluetoothDeviceModel(
      id: device.remoteId.str,
      name: device.platformName.isNotEmpty
          ? device.platformName
          : 'Unknown Device',
      address: device.remoteId.str,
      type: DeviceType
          .unknown, // flutter_blue_plus doesn't expose device type directly
      isConnected: device.isConnected,
    );
  }

  /// Create from ScanResult
  factory BluetoothDeviceModel.fromScanResult(ScanResult scanResult) {
    return BluetoothDeviceModel(
      id: scanResult.device.remoteId.str,
      name: scanResult.advertisementData.advName.isNotEmpty
          ? scanResult.advertisementData.advName
          : scanResult.device.platformName.isNotEmpty
              ? scanResult.device.platformName
              : 'Unknown Device',
      address: scanResult.device.remoteId.str,
      type: DeviceType
          .unknown, // flutter_blue_plus doesn't expose device type directly
      isConnected: scanResult.device.isConnected,
      rssi: scanResult.rssi,
      lastSeen: DateTime.now(),
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'type': type.index,
      'isConnected': isConnected,
      'isAlarmEnabled': isAlarmEnabled,
      'alarmTriggerType': alarmTriggerType.index,
      'customAlarmSound': customAlarmSound,
      'lastSeen': lastSeen?.millisecondsSinceEpoch,
      'rssi': rssi,
    };
  }

  /// Create from JSON
  factory BluetoothDeviceModel.fromJson(Map<String, dynamic> json) {
    return BluetoothDeviceModel(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      type: DeviceType.values[json['type'] ?? 0],
      isConnected: json['isConnected'] ?? false,
      isAlarmEnabled: json['isAlarmEnabled'] ?? false,
      alarmTriggerType: AlarmTriggerType.values[json['alarmTriggerType'] ?? 0],
      customAlarmSound: json['customAlarmSound'],
      lastSeen: json['lastSeen'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastSeen'])
          : null,
      rssi: json['rssi'],
    );
  }

  /// Copy with new values
  BluetoothDeviceModel copyWith({
    String? id,
    String? name,
    String? address,
    DeviceType? type,
    bool? isConnected,
    bool? isAlarmEnabled,
    AlarmTriggerType? alarmTriggerType,
    String? customAlarmSound,
    DateTime? lastSeen,
    int? rssi,
  }) {
    return BluetoothDeviceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      type: type ?? this.type,
      isConnected: isConnected ?? this.isConnected,
      isAlarmEnabled: isAlarmEnabled ?? this.isAlarmEnabled,
      alarmTriggerType: alarmTriggerType ?? this.alarmTriggerType,
      customAlarmSound: customAlarmSound ?? this.customAlarmSound,
      lastSeen: lastSeen ?? this.lastSeen,
      rssi: rssi ?? this.rssi,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BluetoothDeviceModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'BluetoothDeviceModel(id: $id, name: $name, address: $address, isConnected: $isConnected)';
  }
}

/// Types of alarm triggers
enum AlarmTriggerType {
  onDisconnect,
  onConnect,
  both,
}

/// Extension for AlarmTriggerType
extension AlarmTriggerTypeExtension on AlarmTriggerType {
  String get displayName {
    switch (this) {
      case AlarmTriggerType.onDisconnect:
        return 'On Disconnect';
      case AlarmTriggerType.onConnect:
        return 'On Connect';
      case AlarmTriggerType.both:
        return 'Both Connect & Disconnect';
    }
  }

  String get description {
    switch (this) {
      case AlarmTriggerType.onDisconnect:
        return 'Alarm when device goes out of range or powers off';
      case AlarmTriggerType.onConnect:
        return 'Alarm when device comes into range or powers on';
      case AlarmTriggerType.both:
        return 'Alarm on both connection and disconnection events';
    }
  }
}
