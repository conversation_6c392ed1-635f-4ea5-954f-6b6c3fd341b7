import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/alarm_provider.dart';

/// Settings screen for app configuration
class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildBluetoothSection(context),
          const SizedBox(height: 24),
          _buildAlarmSection(context),
          const SizedBox(height: 24),
          _buildAppSection(context),
          const SizedBox(height: 24),
          _buildAboutSection(context),
        ],
      ),
    );
  }

  Widget _buildBluetoothSection(BuildContext context) {
    return Consumer<BluetoothProvider>(
      builder: (context, bluetoothProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Bluetooth',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: Icon(
                    bluetoothProvider.isBluetoothEnabled 
                        ? Icons.bluetooth 
                        : Icons.bluetooth_disabled,
                    color: bluetoothProvider.isBluetoothEnabled 
                        ? Colors.blue 
                        : Colors.grey,
                  ),
                  title: const Text('Bluetooth Status'),
                  subtitle: Text(
                    bluetoothProvider.isBluetoothEnabled 
                        ? 'Enabled' 
                        : 'Disabled',
                  ),
                  trailing: bluetoothProvider.isBluetoothEnabled 
                      ? null 
                      : ElevatedButton(
                          onPressed: () => bluetoothProvider.turnOnBluetooth(),
                          child: const Text('Enable'),
                        ),
                ),
                ListTile(
                  leading: Icon(
                    bluetoothProvider.hasRequiredPermissions 
                        ? Icons.check_circle 
                        : Icons.warning,
                    color: bluetoothProvider.hasRequiredPermissions 
                        ? Colors.green 
                        : Colors.orange,
                  ),
                  title: const Text('Permissions'),
                  subtitle: Text(
                    bluetoothProvider.hasRequiredPermissions 
                        ? 'All permissions granted' 
                        : 'Some permissions missing',
                  ),
                  trailing: bluetoothProvider.hasRequiredPermissions 
                      ? null 
                      : ElevatedButton(
                          onPressed: () => bluetoothProvider.requestPermissions(),
                          child: const Text('Grant'),
                        ),
                ),
                ListTile(
                  leading: const Icon(Icons.devices),
                  title: const Text('Paired Devices'),
                  subtitle: Text('${bluetoothProvider.pairedDevices.length} devices'),
                  trailing: IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: () => bluetoothProvider.refreshPairedDevices(),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAlarmSection(BuildContext context) {
    return Consumer<AlarmProvider>(
      builder: (context, alarmProvider, child) {
        final devicesWithAlarms = alarmProvider.getDevicesWithAlarmsEnabled();
        final activeAlarmCount = alarmProvider.getActiveAlarmCount();

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Alarms',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.alarm_on, color: Colors.blue),
                  title: const Text('Enabled Alarms'),
                  subtitle: Text('${devicesWithAlarms.length} devices have alarms enabled'),
                ),
                ListTile(
                  leading: Icon(
                    Icons.alarm,
                    color: activeAlarmCount > 0 ? Colors.red : Colors.grey,
                  ),
                  title: const Text('Active Alarms'),
                  subtitle: Text('$activeAlarmCount alarms currently active'),
                  trailing: activeAlarmCount > 0 
                      ? ElevatedButton(
                          onPressed: () {
                            alarmProvider.stopAllAlarms();
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('All alarms stopped'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Stop All'),
                        )
                      : null,
                ),
                ListTile(
                  leading: const Icon(Icons.history),
                  title: const Text('Recent Events'),
                  subtitle: Text('${alarmProvider.recentAlarmEvents.length} recent alarm events'),
                  trailing: alarmProvider.recentAlarmEvents.isNotEmpty
                      ? TextButton(
                          onPressed: () {
                            alarmProvider.clearRecentEvents();
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Recent events cleared'),
                              ),
                            );
                          },
                          child: const Text('Clear'),
                        )
                      : null,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAppSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'App Settings',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.dark_mode),
              title: const Text('Dark Mode'),
              subtitle: const Text('Toggle dark theme'),
              trailing: Switch(
                value: Theme.of(context).brightness == Brightness.dark,
                onChanged: (value) {
                  // This would be implemented with a theme provider
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Theme switching will be implemented'),
                    ),
                  );
                },
              ),
            ),
            ListTile(
              leading: const Icon(Icons.notifications),
              title: const Text('Notifications'),
              subtitle: const Text('Manage notification settings'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Notification settings will be implemented'),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.storage),
              title: const Text('Clear Data'),
              subtitle: const Text('Reset all app data'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showClearDataDialog(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'About',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            const ListTile(
              leading: Icon(Icons.info),
              title: Text('Version'),
              subtitle: Text('1.0.0'),
            ),
            const ListTile(
              leading: Icon(Icons.description),
              title: Text('Description'),
              subtitle: Text('Bluetooth Device Manager with Alarm functionality'),
            ),
            ListTile(
              leading: const Icon(Icons.bug_report),
              title: const Text('Report Issue'),
              subtitle: const Text('Send feedback or report bugs'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Bug reporting will be implemented'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearDataDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Clear All Data'),
          content: const Text(
            'This will remove all saved device settings and alarm configurations. This action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Clear data logic would go here
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Data clearing will be implemented'),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Clear Data'),
            ),
          ],
        );
      },
    );
  }
}
