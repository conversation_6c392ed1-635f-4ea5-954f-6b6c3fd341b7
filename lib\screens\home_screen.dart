import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/bluetooth_provider.dart';
import '../providers/alarm_provider.dart';
import '../services/alarm_service.dart';
import '../widgets/device_tile.dart';
import 'scanning_screen.dart';
import 'settings_screen.dart';

/// Home screen displaying paired Bluetooth devices
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    _initializeProviders();
  }

  Future<void> _initializeProviders() async {
    final bluetoothProvider = context.read<BluetoothProvider>();
    final alarmProvider = context.read<AlarmProvider>();

    await Future.wait([
      bluetoothProvider.initialize(),
      alarmProvider.initialize(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bluetooth Device Manager'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer<AlarmProvider>(
            builder: (context, alarmProvider, child) {
              final activeAlarmCount = alarmProvider.getActiveAlarmCount();
              return Stack(
                children: [
                  IconButton(
                    icon: const Icon(Icons.notifications),
                    onPressed: () => _showAlarmEventsDialog(context),
                  ),
                  if (activeAlarmCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '$activeAlarmCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const SettingsScreen()),
            ),
          ),
        ],
      ),
      body: Consumer<BluetoothProvider>(
        builder: (context, bluetoothProvider, child) {
          if (!bluetoothProvider.isInitialized) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (bluetoothProvider.errorMessage != null) {
            return _buildErrorView(bluetoothProvider);
          }

          if (!bluetoothProvider.hasRequiredPermissions) {
            return _buildPermissionView(bluetoothProvider);
          }

          if (!bluetoothProvider.isBluetoothEnabled) {
            return _buildBluetoothDisabledView(bluetoothProvider);
          }

          return _buildDeviceList(bluetoothProvider);
        },
      ),
      floatingActionButton: Consumer<BluetoothProvider>(
        builder: (context, bluetoothProvider, child) {
          if (!bluetoothProvider.isBluetoothEnabled ||
              !bluetoothProvider.hasRequiredPermissions) {
            return const SizedBox.shrink();
          }

          return FloatingActionButton.extended(
            onPressed: bluetoothProvider.isScanning
                ? null
                : () => _navigateToScanningScreen(context),
            icon: bluetoothProvider.isScanning
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.bluetooth_searching),
            label: Text(
                bluetoothProvider.isScanning ? 'Scanning...' : 'Scan Devices'),
          );
        },
      ),
    );
  }

  Widget _buildErrorView(BluetoothProvider bluetoothProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              bluetoothProvider.errorMessage!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                bluetoothProvider.clearError();
                _initializeProviders();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionView(BluetoothProvider bluetoothProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.security,
              size: 64,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            Text(
              'Permissions Required',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              'This app needs Bluetooth permissions to scan for and connect to devices.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => bluetoothProvider.requestPermissions(),
              child: const Text('Grant Permissions'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBluetoothDisabledView(BluetoothProvider bluetoothProvider) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.bluetooth_disabled,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'Bluetooth Disabled',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              'Please enable Bluetooth to use this app.',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => bluetoothProvider.turnOnBluetooth(),
              child: const Text('Enable Bluetooth'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceList(BluetoothProvider bluetoothProvider) {
    final pairedDevices = bluetoothProvider.pairedDevices;

    if (pairedDevices.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.bluetooth,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              Text(
                'No Paired Devices',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              const Text(
                'Tap the scan button to find and pair with nearby Bluetooth devices.',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () => bluetoothProvider.refreshPairedDevices(),
      child: ListView.builder(
        padding: const EdgeInsets.all(8.0),
        itemCount: pairedDevices.length,
        itemBuilder: (context, index) {
          final device = pairedDevices[index];
          return DeviceTile(
            device: device,
            onTap: () => _showDeviceOptions(context, device.id),
          );
        },
      ),
    );
  }

  void _navigateToScanningScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ScanningScreen(),
      ),
    );
  }

  void _showDeviceOptions(BuildContext context, String deviceId) {
    // This will be implemented when we create the alarm configuration screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Device options for $deviceId'),
        action: SnackBarAction(
          label: 'Configure',
          onPressed: () {
            // Navigate to alarm configuration
          },
        ),
      ),
    );
  }

  void _showAlarmEventsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Consumer<AlarmProvider>(
        builder: (context, alarmProvider, child) {
          final events = alarmProvider.recentAlarmEvents.take(10).toList();

          return AlertDialog(
            title: const Text('Recent Alarm Events'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: events.isEmpty
                  ? const Center(child: Text('No recent alarm events'))
                  : ListView.builder(
                      itemCount: events.length,
                      itemBuilder: (context, index) {
                        final event = events[index];
                        return ListTile(
                          leading: Icon(
                            event.type == AlarmEventType.triggered
                                ? Icons.alarm
                                : event.type == AlarmEventType.stopped
                                    ? Icons.alarm_off
                                    : Icons.science,
                            color: event.type == AlarmEventType.triggered
                                ? Colors.red
                                : Colors.grey,
                          ),
                          title: Text(event.deviceName.isNotEmpty
                              ? event.deviceName
                              : event.deviceId),
                          subtitle: Text(
                            '${event.type.name} - ${event.timestamp.toString().substring(0, 19)}',
                          ),
                        );
                      },
                    ),
            ),
            actions: [
              TextButton(
                onPressed: () => alarmProvider.clearRecentEvents(),
                child: const Text('Clear'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          );
        },
      ),
    );
  }
}
